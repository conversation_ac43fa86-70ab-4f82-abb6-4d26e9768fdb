<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>电子病历系统 - WebRTC推流测试</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            padding: 20px;
            min-height: 600px; /* 确保最小高度，防止布局跳动 */
        }
        
        .video-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .control-section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
        }
        
        .video-container {
            position: relative;
            background: #000;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 15px;
        }
        
        #localVideo {
            width: 100%;
            height: 300px;
            object-fit: cover;
            display: block; /* 确保视频元素正确显示 */
        }
        
        .video-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input,
        .form-group select {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(79, 172, 254, 0.4);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
            color: white;
        }
        
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 107, 107, 0.4);
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 录制控制样式 */
        .recording-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-left: 4px solid #dc3545;
        }

        .recording-section h4 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #dc3545;
            font-weight: bold;
            font-size: 18px;
        }

        .recording-group {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
        }

        .recording-group h5 {
            margin-top: 0;
            margin-bottom: 12px;
            color: #495057;
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .recording-status-display {
            background: white;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 15px;
            border: 1px solid #dee2e6;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }

        .status-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            min-height: 24px;
        }

        .status-row:last-child {
            margin-bottom: 0;
        }

        .status-label {
            font-weight: 500;
            color: #495057;
            font-size: 14px;
        }

        .status-indicator {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            min-width: 60px;
            text-align: center;
        }

        .status-stopped {
            background-color: #6c757d;
            color: white;
        }

        .status-starting {
            background-color: #ffc107;
            color: #212529;
        }

        .status-recording {
            background-color: #dc3545;
            color: white;
            animation: pulse 2s infinite;
        }

        .status-stopping {
            background-color: #fd7e14;
            color: white;
        }

        .status-error {
            background-color: #dc3545;
            color: white;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .duration-display {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #dc3545;
            font-size: 14px;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .file-display {
            font-family: 'Courier New', monospace;
            font-size: 11px;
            color: #6c757d;
            max-width: 180px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
        }

        .btn-record {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
            border: none;
            font-size: 14px;
            padding: 10px 16px;
        }

        .btn-record:hover:not(:disabled) {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3);
        }

        .btn-stop-record {
            background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
            color: white;
            border: none;
            font-size: 14px;
            padding: 10px 16px;
        }

        .btn-stop-record:hover:not(:disabled) {
            background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
        }

        .recording-info {
            margin-top: 15px;
            padding: 12px;
            background: linear-gradient(135deg, #e7f3ff 0%, #f0f8ff 100%);
            border-radius: 6px;
            border-left: 3px solid #007bff;
            font-size: 13px;
            line-height: 1.4;
        }
        
        .status-panel {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 20px;
            border-left: 4px solid #4facfe;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .status-label {
            font-weight: bold;
            color: #333;
        }
        
        .status-value {
            color: #666;
        }
        
        .status-value.success {
            color: #28a745;
            font-weight: bold;
        }
        
        .status-value.error {
            color: #dc3545;
            font-weight: bold;
        }
        
        .status-value.warning {
            color: #ffc107;
            font-weight: bold;
        }
        
        .log-panel {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.info {
            color: #63b3ed;
        }
        
        .log-entry.success {
            color: #68d391;
        }
        
        .log-entry.warning {
            color: #fbd38d;
        }
        
        .log-entry.error {
            color: #fc8181;
        }
        
        .log-timestamp {
            color: #a0aec0;
            margin-right: 10px;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .button-group {
                flex-direction: column;
            }

            .recording-group {
                padding: 12px;
            }

            .recording-status-display {
                padding: 10px;
            }

            .status-row {
                flex-direction: column;
                align-items: flex-start;
                gap: 4px;
            }

            .status-indicator {
                align-self: flex-end;
            }

            .file-display {
                max-width: 100%;
                word-break: break-all;
            }
        }

        /* 录制状态动画效果 */
        .status-indicator {
            transition: all 0.3s ease;
        }

        .status-recording {
            box-shadow: 0 0 10px rgba(220, 53, 69, 0.5);
        }

        .recording-group {
            transition: all 0.3s ease;
        }

        .recording-group:hover {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>电子病历生成系统</h1>
            <p>WebRTC推流测试 - SRS 6.0服务器连接测试</p>
        </div>
        
        <div class="main-content">
            <!-- 视频预览区域 -->
            <div class="video-section">
                <h3>视频预览</h3>
                <div class="video-container">
                    <video id="localVideo" autoplay muted playsinline></video>
                    <div class="video-info" id="videoInfo">
                        等待摄像头...
                    </div>
                </div>
                
                <!-- 连接状态面板 -->
                <div class="status-panel">
                    <h4>连接状态</h4>
                    <div class="status-item">
                        <span class="status-label">WebRTC状态:</span>
                        <span class="status-value" id="webrtcStatus">未连接</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">ICE连接状态:</span>
                        <span class="status-value" id="iceStatus">未连接</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">DTLS状态:</span>
                        <span class="status-value" id="dtlsStatus">未连接</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">推流时长:</span>
                        <span class="status-value" id="streamDuration">00:00:00</span>
                    </div>
                </div>
                
                <!-- 媒体统计信息 -->
                <div class="status-panel">
                    <h4>媒体统计</h4>
                    <div class="status-item">
                        <span class="status-label">视频分辨率:</span>
                        <span class="status-value" id="videoResolutionDisplay">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">视频帧率:</span>
                        <span class="status-value" id="videoFramerateDisplay">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">视频码率:</span>
                        <span class="status-value" id="videoBitrate">-</span>
                    </div>
                    <div class="status-item">
                        <span class="status-label">音频码率:</span>
                        <span class="status-value" id="audioBitrate">-</span>
                    </div>
                </div>
            </div>
            
            <!-- 控制面板 -->
            <div class="control-section">
                <h3>推流控制</h3>
                
                <!-- 服务器配置 -->
                <div class="form-group">
                    <label for="serverUrl">SRS服务器地址:</label>
                    <input type="text" id="serverUrl" value="http://**************:1985" placeholder="http://**************:1985">
                </div>
                
                <div class="form-group">
                    <label for="streamKey">推流密钥:</label>
                    <input type="text" id="streamKey" value="test" placeholder="推流密钥">
                </div>
                
                <!-- 媒体配置 -->
                <div class="form-group">
                    <label for="videoResolution">视频分辨率:</label>
                    <select id="videoResolution">
                        <option value="640x480">640x480 (VGA)</option>
                        <option value="1280x720" selected>1280x720 (HD)</option>
                        <option value="1920x1080">1920x1080 (FHD)</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="videoFramerate">视频帧率:</label>
                    <select id="videoFramerate">
                        <option value="15">15 FPS</option>
                        <option value="24">24 FPS</option>
                        <option value="30" selected>30 FPS</option>
                        <option value="60">60 FPS</option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label for="videoBitrate">视频码率 (kbps):</label>
                    <select id="videoBitrate">
                        <option value="500">500 kbps</option>
                        <option value="1000">1000 kbps</option>
                        <option value="2000" selected>2000 kbps</option>
                        <option value="4000">4000 kbps</option>
                    </select>
                </div>
                
                <!-- 控制按钮 -->
                <div class="button-group">
                    <button class="btn btn-primary" id="startBtn" onclick="startStream()">开始推流</button>
                    <button class="btn btn-danger" id="stopBtn" onclick="stopStream()" disabled>停止推流</button>
                </div>

                <!-- 录制控制 -->
                <div class="recording-section">
                    <h4>📹 录制控制</h4>

                    <!-- 视频录制控制 -->
                    <div class="recording-group">
                        <h5>🎥 视频录制 (MP4)</h5>
                        <div class="recording-status-display">
                            <div class="status-row">
                                <span class="status-label">状态:</span>
                                <span id="videoRecordingStatus" class="status-indicator status-stopped">未录制</span>
                            </div>
                            <div class="status-row">
                                <span class="status-label">时长:</span>
                                <span id="videoRecordingDuration" class="duration-display">00:00:00</span>
                            </div>
                            <div class="status-row">
                                <span class="status-label">文件:</span>
                                <span id="videoRecordingFile" class="file-display">-</span>
                            </div>
                        </div>
                        <div class="button-group">
                            <button class="btn btn-record" id="startVideoRecordingBtn" onclick="startVideoRecording()" disabled>
                                🔴 开始视频录制
                            </button>
                            <button class="btn btn-stop-record" id="stopVideoRecordingBtn" onclick="stopVideoRecording()" disabled>
                                ⏹️ 停止视频录制
                            </button>
                        </div>
                    </div>

                    <!-- 音频录制控制 -->
                    <div class="recording-group">
                        <h5>🎵 音频录制 (WAV)</h5>
                        <div class="recording-status-display">
                            <div class="status-row">
                                <span class="status-label">状态:</span>
                                <span id="audioRecordingStatus" class="status-indicator status-stopped">未录制</span>
                            </div>
                            <div class="status-row">
                                <span class="status-label">时长:</span>
                                <span id="audioRecordingDuration" class="duration-display">00:00:00</span>
                            </div>
                            <div class="status-row">
                                <span class="status-label">文件:</span>
                                <span id="audioRecordingFile" class="file-display">-</span>
                            </div>
                        </div>
                        <div class="button-group">
                            <button class="btn btn-record" id="startAudioRecordingBtn" onclick="startAudioRecording()" disabled>
                                🎤 开始音频录制
                            </button>
                            <button class="btn btn-stop-record" id="stopAudioRecordingBtn" onclick="stopAudioRecording()" disabled>
                                ⏹️ 停止音频录制
                            </button>
                        </div>
                    </div>

                    <div class="recording-info">
                        <small id="recordingInfo" class="text-muted">
                            💡 录制功能需要先开始推流，视频和音频可以独立录制
                        </small>
                    </div>
                </div>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="testConnection()">测试连接</button>
                    <button class="btn btn-primary" onclick="clearLogs()">清空日志</button>
                </div>
                
                <!-- 调试日志 -->
                <h4>调试日志</h4>
                <div class="log-panel" id="logPanel">
                    <div class="log-entry info">
                        <span class="log-timestamp">[系统]</span>
                        页面加载完成，等待用户操作...
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let localStream = null;
        let peerConnection = null;
        let isStreaming = false;
        let streamStartTime = null;

        // 录制相关变量
        let recordingServerUrl = 'http://**************:5410';

        // 视频录制状态
        let isVideoRecording = false;
        let videoRecordingStartTime = null;
        let videoRecordingDurationTimer = null;
        let currentVideoTaskId = null;

        // 音频录制状态
        let isAudioRecording = false;
        let audioRecordingStartTime = null;
        let audioRecordingDurationTimer = null;
        let currentAudioTaskId = null;
        let durationTimer = null;
        let statsTimer = null;
        
        // WebRTC配置
        const rtcConfig = {
            iceServers: [
                { urls: 'stun:stun.l.google.com:19302' },
                { urls: 'stun:stun1.l.google.com:19302' }
            ],
            iceCandidatePoolSize: 10
        };
        
        // 日志函数
        function addLog(message, type = 'info') {
            const logPanel = document.getElementById('logPanel');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.innerHTML = `<span class="log-timestamp">[${timestamp}]</span>${message}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;
        }
        
        // 更新状态显示
        function updateStatus(elementId, value, type = '') {
            const element = document.getElementById(elementId);
            element.textContent = value;
            element.className = `status-value ${type}`;
        }
        
        // 清空日志
        function clearLogs() {
            document.getElementById('logPanel').innerHTML = '';
            addLog('日志已清空');
        }
        
        // 格式化时长
        function formatDuration(milliseconds) {
            const totalSeconds = Math.floor(milliseconds / 1000);
            const hours = Math.floor(totalSeconds / 3600);
            const minutes = Math.floor((totalSeconds % 3600) / 60);
            const secs = totalSeconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }
        
        // 更新推流时长
        function updateDuration() {
            if (streamStartTime) {
                const duration = Math.floor((Date.now() - streamStartTime) / 1000);
                updateStatus('streamDuration', formatDuration(duration));
            }
        }
        
        // 获取媒体流
        async function getMediaStream() {
            try {
                addLog('正在获取摄像头和麦克风权限...', 'info');

                // 获取配置参数并添加错误检查
                const resolutionElement = document.getElementById('videoResolution');
                const framerateElement = document.getElementById('videoFramerate');

                if (!resolutionElement || !framerateElement) {
                    throw new Error('无法找到视频配置元素');
                }

                const resolutionValue = resolutionElement.value;
                const framerateValue = framerateElement.value;

                if (!resolutionValue || !framerateValue) {
                    throw new Error('视频配置参数为空');
                }

                addLog(`配置参数: 分辨率=${resolutionValue}, 帧率=${framerateValue}`, 'info');

                const resolution = resolutionValue.split('x');
                const framerate = parseInt(framerateValue);

                if (resolution.length !== 2 || isNaN(framerate)) {
                    throw new Error('视频配置参数格式错误');
                }
                
                const constraints = {
                    video: {
                        width: { ideal: parseInt(resolution[0]) },
                        height: { ideal: parseInt(resolution[1]) },
                        frameRate: { ideal: framerate }
                    },
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                };
                
                addLog('请求媒体设备权限...', 'info');
                localStream = await navigator.mediaDevices.getUserMedia(constraints);

                if (!localStream) {
                    throw new Error('获取媒体流返回空值');
                }

                addLog('媒体流获取成功，设置视频元素...', 'success');

                const videoElement = document.getElementById('localVideo');
                if (!videoElement) {
                    throw new Error('无法找到视频元素');
                }

                videoElement.srcObject = localStream;

                // 等待视频元数据加载
                await new Promise((resolve, reject) => {
                    videoElement.onloadedmetadata = resolve;
                    videoElement.onerror = reject;
                    setTimeout(() => reject(new Error('视频加载超时')), 5000);
                });

                // 更新视频信息
                const videoTracks = localStream.getVideoTracks();
                const audioTracks = localStream.getAudioTracks();

                addLog(`获取到 ${videoTracks.length} 个视频轨道, ${audioTracks.length} 个音频轨道`, 'info');

                if (videoTracks.length > 0) {
                    const videoTrack = videoTracks[0];
                    const settings = videoTrack.getSettings();

                    updateStatus('videoResolutionDisplay', `${settings.width}x${settings.height}`);
                    updateStatus('videoFramerateDisplay', `${settings.frameRate} FPS`);

                    document.getElementById('videoInfo').textContent =
                        `${settings.width}x${settings.height} @ ${settings.frameRate}fps`;

                    addLog(`视频轨道设置: ${JSON.stringify(settings)}`, 'info');
                }

                if (audioTracks.length > 0) {
                    const audioTrack = audioTracks[0];
                    const settings = audioTrack.getSettings();
                    addLog(`音频轨道设置: ${JSON.stringify(settings)}`, 'info');
                }

                addLog('媒体流配置完成', 'success');
                return true;

            } catch (error) {
                let errorMessage = error.message;
                let errorType = 'error';

                // 根据错误类型提供更详细的信息
                if (error.name === 'NotAllowedError') {
                    errorMessage = '用户拒绝了摄像头/麦克风权限请求';
                    errorType = 'warning';
                } else if (error.name === 'NotFoundError') {
                    errorMessage = '未找到摄像头或麦克风设备';
                } else if (error.name === 'NotReadableError') {
                    errorMessage = '摄像头或麦克风被其他应用占用';
                } else if (error.name === 'OverconstrainedError') {
                    errorMessage = '设备不支持请求的媒体约束条件';
                } else if (error.name === 'SecurityError') {
                    errorMessage = '安全错误：请使用HTTPS访问或localhost';
                }

                addLog(`获取媒体流失败: ${errorMessage}`, errorType);
                addLog(`错误详情: ${error.name} - ${error.message}`, 'error');
                updateStatus('webrtcStatus', '媒体流获取失败', 'error');

                // 清理可能的部分资源
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                    localStream = null;
                }

                return false;
            }
        }
        
        // 创建WebRTC连接
        async function createPeerConnection() {
            try {
                peerConnection = new RTCPeerConnection(rtcConfig);
                
                // 监听ICE候选
                peerConnection.onicecandidate = (event) => {
                    if (event.candidate) {
                        addLog(`ICE候选: ${event.candidate.candidate}`, 'info');
                    } else {
                        addLog('ICE候选收集完成', 'success');
                    }
                };
                
                // 监听连接状态变化
                peerConnection.onconnectionstatechange = () => {
                    const state = peerConnection.connectionState;
                    addLog(`连接状态变化: ${state}`, state === 'connected' ? 'success' : 'info');
                    updateStatus('webrtcStatus', state, state === 'connected' ? 'success' : '');
                };
                
                // 监听ICE连接状态变化
                peerConnection.oniceconnectionstatechange = () => {
                    const state = peerConnection.iceConnectionState;
                    addLog(`ICE连接状态: ${state}`, state === 'connected' ? 'success' : 'info');
                    updateStatus('iceStatus', state, state === 'connected' ? 'success' : '');
                };
                
                // 添加媒体流
                localStream.getTracks().forEach(track => {
                    peerConnection.addTrack(track, localStream);
                    addLog(`添加媒体轨道: ${track.kind}`, 'info');
                });
                
                return true;
                
            } catch (error) {
                addLog(`创建WebRTC连接失败: ${error.message}`, 'error');
                return false;
            }
        }
        
        // 开始推流
        async function startStream() {
            try {
                addLog('开始推流流程...', 'info');
                
                // 禁用开始按钮
                document.getElementById('startBtn').disabled = true;
                updateStatus('webrtcStatus', '连接中...', 'warning');
                
                // 获取媒体流
                if (!localStream) {
                    const success = await getMediaStream();
                    if (!success) {
                        document.getElementById('startBtn').disabled = false;
                        return;
                    }
                }
                
                // 创建WebRTC连接
                const success = await createPeerConnection();
                if (!success) {
                    document.getElementById('startBtn').disabled = false;
                    return;
                }
                
                // 创建Offer
                addLog('创建WebRTC Offer...', 'info');
                const offer = await peerConnection.createOffer();
                await peerConnection.setLocalDescription(offer);
                
                // 发送到SRS服务器 - 使用SRS 6.0 WebRTC API格式
                const serverUrl = document.getElementById('serverUrl').value;
                const streamKey = document.getElementById('streamKey').value;

                // 解析服务器URL获取主机和端口
                const urlObj = new URL(serverUrl);
                const host = urlObj.hostname;
                const apiPort = urlObj.port || '1985';

                // 构建API URL和Stream URL
                // 注意：SRS的WebRTC流URL使用8000端口（UDP），而API使用1985端口（TCP）
                const apiUrl = `${urlObj.protocol}//${host}:${apiPort}/rtc/v1/publish/`;
                const streamUrl = `webrtc://${host}:8000/live/${streamKey}`;

                addLog(`连接到SRS服务器: ${apiUrl}`, 'info');
                addLog(`推流地址: ${streamUrl}`, 'info');

                // SRS WebRTC API请求格式（基于官方SDK）
                const requestData = {
                    api: apiUrl,
                    tid: Number(parseInt(new Date().getTime() * Math.random() * 100)).toString(16).slice(0, 7),
                    streamurl: streamUrl,
                    clientip: null,
                    sdp: offer.sdp
                };

                addLog(`请求数据: ${JSON.stringify(requestData, null, 2)}`, 'info');

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`SRS服务器响应错误: ${response.status} ${response.statusText} - ${errorText}`);
                }

                const responseData = await response.json();
                addLog(`SRS响应: ${JSON.stringify(responseData, null, 2)}`, 'info');

                if (responseData.code && responseData.code !== 0) {
                    throw new Error(`SRS服务器错误: ${responseData.code} - ${responseData.msg || '未知错误'}`);
                }

                const answerSdp = responseData.sdp;
                addLog('收到SRS服务器Answer', 'success');
                
                // 设置远程描述
                await peerConnection.setRemoteDescription({
                    type: 'answer',
                    sdp: answerSdp
                });
                
                // 更新状态
                isStreaming = true;
                streamStartTime = Date.now();
                document.getElementById('stopBtn').disabled = false;
                updateStatus('webrtcStatus', '推流中', 'success');
                updateStatus('dtlsStatus', '已连接', 'success');

                // 启用录制控制
                enableRecordingControls(true);
                
                // 开始统计定时器
                durationTimer = setInterval(updateDuration, 1000);
                statsTimer = setInterval(updateStats, 2000);
                
                addLog('WebRTC推流启动成功！', 'success');
                
            } catch (error) {
                addLog(`推流失败: ${error.message}`, 'error');
                updateStatus('webrtcStatus', '连接失败', 'error');
                document.getElementById('startBtn').disabled = false;
                
                // 清理资源
                if (peerConnection) {
                    peerConnection.close();
                    peerConnection = null;
                }
            }
        }
        
        // 停止推流
        async function stopStream() {
            try {
                addLog('停止推流...', 'info');
                
                // 清理定时器
                if (durationTimer) {
                    clearInterval(durationTimer);
                    durationTimer = null;
                }
                
                if (statsTimer) {
                    clearInterval(statsTimer);
                    statsTimer = null;
                }
                
                // 关闭WebRTC连接
                if (peerConnection) {
                    peerConnection.close();
                    peerConnection = null;
                }
                
                // 停止媒体流
                if (localStream) {
                    localStream.getTracks().forEach(track => track.stop());
                    localStream = null;
                }
                
                // 清空视频元素
                document.getElementById('localVideo').srcObject = null;

                // 停止视频录制（如果正在录制）
                if (isVideoRecording) {
                    await stopVideoRecording();
                }

                // 停止音频录制（如果正在录制）
                if (isAudioRecording) {
                    await stopAudioRecording();
                }

                // 重置状态
                isStreaming = false;
                streamStartTime = null;
                document.getElementById('startBtn').disabled = false;
                document.getElementById('stopBtn').disabled = true;

                updateStatus('webrtcStatus', '已断开', '');
                updateStatus('iceStatus', '已断开', '');
                updateStatus('dtlsStatus', '已断开', '');

                // 禁用录制控制
                enableRecordingControls(false);
                updateStatus('streamDuration', '00:00:00', '');
                updateStatus('videoResolutionDisplay', '-', '');
                updateStatus('videoFramerateDisplay', '-', '');
                updateStatus('videoBitrate', '-', '');
                updateStatus('audioBitrate', '-', '');
                
                document.getElementById('videoInfo').textContent = '等待摄像头...';
                
                addLog('推流已停止', 'success');
                
            } catch (error) {
                addLog(`停止推流时出错: ${error.message}`, 'error');
            }
        }
        
        // 更新统计信息
        async function updateStats() {
            if (!peerConnection || !isStreaming) return;
            
            try {
                const stats = await peerConnection.getStats();
                let videoBitrate = 0;
                let audioBitrate = 0;
                
                stats.forEach(report => {
                    if (report.type === 'outbound-rtp') {
                        if (report.kind === 'video' && report.bytesSent) {
                            videoBitrate = Math.round(report.bytesSent * 8 / 1000); // kbps
                        } else if (report.kind === 'audio' && report.bytesSent) {
                            audioBitrate = Math.round(report.bytesSent * 8 / 1000); // kbps
                        }
                    }
                });
                
                if (videoBitrate > 0) {
                    updateStatus('videoBitrate', `${videoBitrate} kbps`);
                }
                
                if (audioBitrate > 0) {
                    updateStatus('audioBitrate', `${audioBitrate} kbps`);
                }
                
            } catch (error) {
                addLog(`获取统计信息失败: ${error.message}`, 'warning');
            }
        }
        
        // 测试连接
        async function testConnection() {
            const serverUrl = document.getElementById('serverUrl').value;
            
            try {
                addLog('测试SRS服务器连接...', 'info');
                
                // 测试API连接
                const apiUrl = `${serverUrl}/api/v1/summaries`;
                const response = await fetch(apiUrl);
                
                if (response.ok) {
                    const data = await response.json();
                    addLog('SRS服务器连接正常', 'success');
                    addLog(`服务器信息: ${JSON.stringify(data, null, 2)}`, 'info');
                } else {
                    addLog(`SRS服务器响应错误: ${response.status}`, 'error');
                }
                
            } catch (error) {
                addLog(`连接测试失败: ${error.message}`, 'error');
            }
        }
        
        // 检查浏览器兼容性
        function checkBrowserCompatibility() {
            const issues = [];
            const warnings = [];

            // 检查基本WebRTC支持
            if (!navigator.mediaDevices) {
                issues.push('不支持 navigator.mediaDevices');
            }

            if (!navigator.mediaDevices.getUserMedia) {
                issues.push('不支持 getUserMedia');
            }

            if (!window.RTCPeerConnection) {
                issues.push('不支持 RTCPeerConnection');
            }

            if (!window.RTCSessionDescription) {
                issues.push('不支持 RTCSessionDescription');
            }

            // 检查安全上下文 - 更智能的检查逻辑
            const isSecureContext = checkSecureContext();
            if (!isSecureContext.isSecure) {
                // 根据具体情况给出不同的处理
                if (isSecureContext.canBypass) {
                    warnings.push(`安全上下文警告: ${isSecureContext.reason}`);
                    warnings.push('建议使用HTTPS或localhost，但将尝试继续运行');
                } else {
                    issues.push(`安全上下文错误: ${isSecureContext.reason}`);
                }
            }

            // 检测浏览器类型
            const userAgent = navigator.userAgent;
            let browserInfo = '未知浏览器';

            if (userAgent.includes('Chrome')) {
                browserInfo = 'Chrome';
            } else if (userAgent.includes('Firefox')) {
                browserInfo = 'Firefox';
            } else if (userAgent.includes('Safari')) {
                browserInfo = 'Safari';
            } else if (userAgent.includes('Edge')) {
                browserInfo = 'Edge';
            }

            addLog(`浏览器信息: ${browserInfo}`, 'info');

            // 显示警告信息
            if (warnings.length > 0) {
                warnings.forEach(warning => {
                    addLog(warning, 'warning');
                });
            }

            if (issues.length > 0) {
                addLog(`浏览器兼容性问题: ${issues.join(', ')}`, 'error');
                updateStatus('webrtcStatus', '浏览器不兼容', 'error');
                return false;
            } else {
                addLog('浏览器兼容性检查通过', 'success');
                return true;
            }
        }

        // 智能安全上下文检查
        function checkSecureContext() {
            const protocol = location.protocol;
            const hostname = location.hostname;
            const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '::1';

            // 1. HTTPS环境 - 完全安全
            if (protocol === 'https:') {
                return {
                    isSecure: true,
                    reason: 'HTTPS环境',
                    canBypass: false
                };
            }

            // 2. localhost环境 - 安全
            if (protocol === 'http:' && isLocalhost) {
                return {
                    isSecure: true,
                    reason: 'localhost环境',
                    canBypass: false
                };
            }

            // 3. file://协议 - 需要特殊处理
            if (protocol === 'file:') {
                // 检查是否支持WebRTC（某些浏览器在file://下仍然支持）
                const hasWebRTCSupport = !!(navigator.mediaDevices && window.RTCPeerConnection);

                if (hasWebRTCSupport) {
                    // 尝试检查是否真的可以使用
                    return {
                        isSecure: true, // 允许尝试
                        reason: 'file://协议，但浏览器支持WebRTC',
                        canBypass: true
                    };
                } else {
                    return {
                        isSecure: false,
                        reason: 'file://协议不支持WebRTC，需要HTTP/HTTPS环境',
                        canBypass: false
                    };
                }
            }

            // 4. HTTP非localhost环境 - 不安全但可能可以绕过
            if (protocol === 'http:') {
                // 检查是否在内网环境
                const isPrivateIP = isPrivateNetwork(hostname);

                if (isPrivateIP) {
                    return {
                        isSecure: false,
                        reason: '内网HTTP环境，WebRTC可能受限',
                        canBypass: true
                    };
                } else {
                    return {
                        isSecure: false,
                        reason: '公网HTTP环境，WebRTC被禁用',
                        canBypass: false
                    };
                }
            }

            // 5. 其他协议
            return {
                isSecure: false,
                reason: `不支持的协议: ${protocol}`,
                canBypass: false
            };
        }

        // 检查是否为内网IP
        function isPrivateNetwork(hostname) {
            // IPv4内网地址范围
            const privateRanges = [
                /^10\./,                    // 10.0.0.0/8
                /^172\.(1[6-9]|2[0-9]|3[01])\./, // **********/12
                /^192\.168\./,              // ***********/16
                /^169\.254\./               // ***********/16 (链路本地)
            ];

            // 检查是否为IP地址
            const ipRegex = /^\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}$/;
            if (ipRegex.test(hostname)) {
                return privateRanges.some(range => range.test(hostname));
            }

            // 对于域名，假设是公网（除非是明显的内网域名）
            const internalDomains = ['.local', '.internal', '.corp', '.lan'];
            return internalDomains.some(domain => hostname.endsWith(domain));
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('WebRTC推流测试页面加载完成', 'success');

            // 初始化录制控制
            initializeRecordingControls();

            // 检查浏览器兼容性
            const compatible = checkBrowserCompatibility();

            if (compatible) {
                addLog('请配置服务器地址后点击"开始推流"', 'info');

                // 显示安全上下文信息
                const securityInfo = checkSecureContext();
                if (securityInfo.canBypass) {
                    addLog('⚠️ 当前环境可能存在安全限制，如遇到问题请参考解决方案', 'warning');
                    showSecuritySolutions();
                }

                // 预检查媒体设备
                if (navigator.mediaDevices && navigator.mediaDevices.enumerateDevices) {
                    navigator.mediaDevices.enumerateDevices()
                        .then(devices => {
                            const videoDevices = devices.filter(device => device.kind === 'videoinput');
                            const audioDevices = devices.filter(device => device.kind === 'audioinput');
                            addLog(`检测到 ${videoDevices.length} 个摄像头, ${audioDevices.length} 个麦克风`, 'info');
                        })
                        .catch(error => {
                            addLog(`设备枚举失败: ${error.message}`, 'warning');
                            addLog('这可能是安全上下文限制导致的，请参考解决方案', 'warning');
                        });
                }
            } else {
                document.getElementById('startBtn').disabled = true;
                addLog('由于浏览器兼容性问题，推流功能已禁用', 'error');
                showSecuritySolutions();
            }
        });

        // 显示安全上下文解决方案
        function showSecuritySolutions() {
            addLog('=== WebRTC安全上下文解决方案 ===', 'info');

            const protocol = location.protocol;
            const hostname = location.hostname;

            if (protocol === 'file:') {
                addLog('📁 当前使用file://协议，推荐解决方案：', 'info');
                addLog('方案1: 启动本地HTTP服务器', 'info');
                addLog('  - Python: python -m http.server 8080', 'info');
                addLog('  - Node.js: npx http-server -p 8080', 'info');
                addLog('  - 然后访问: http://localhost:8080/web/webrtc-test.html', 'info');
                addLog('方案2: 使用Chrome启动参数（仅开发环境）', 'info');
                addLog('  - 关闭所有Chrome窗口', 'info');
                addLog('  - 使用参数启动: --allow-file-access-from-files --disable-web-security', 'info');
                addLog('方案3: 部署到HTTPS服务器', 'info');
            } else if (protocol === 'http:' && hostname !== 'localhost') {
                addLog('🌐 当前使用HTTP协议，推荐解决方案：', 'info');
                addLog('方案1: 使用localhost访问', 'info');
                addLog('方案2: 配置HTTPS证书', 'info');
                addLog('方案3: 使用Chrome开发者模式（临时）', 'info');
            }

            addLog('⚠️ 注意：生产环境必须使用HTTPS', 'warning');
        }



        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (isStreaming) {
                stopStream();
            }
        });

        // ==================== 录制功能实现 ====================

        // 启用/禁用录制控制
        function enableRecordingControls(enable) {
            // 视频录制控制
            document.getElementById('startVideoRecordingBtn').disabled = !enable;

            // 音频录制控制
            document.getElementById('startAudioRecordingBtn').disabled = !enable;

            const info = document.getElementById('recordingInfo');
            if (enable) {
                info.textContent = '💡 推流已开始，可以开始视频和音频录制';
                info.style.color = '#28a745';
            } else {
                info.textContent = '💡 录制功能需要先开始推流，视频和音频可以独立录制';
                info.style.color = '#6c757d';
            }
        }

        // 开始录制
        async function startRecording() {
            if (!isStreaming) {
                addLog('请先开始推流', 'warning');
                return;
            }

            if (isRecording) {
                addLog('录制已在进行中', 'warning');
                return;
            }

            try {
                const streamKey = document.getElementById('streamKey').value;
                if (!streamKey) {
                    addLog('请输入推流密钥', 'error');
                    return;
                }

                // 生成任务ID
                const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
                currentTaskId = `TASK${timestamp}`;

                addLog('开始录制...', 'info');
                updateRecordingStatus('starting', '启动中...');

                const taskData = {
                    task_id: currentTaskId,
                    stream_key: streamKey,
                    userid: 'web_user',
                    username: '网页用户',
                    chief_complaint: '网页测试录制',
                    present_illness: '通过网页进行的WebRTC推流录制测试',
                    past_medical_history: '无',
                    allergic_history: '无',
                    metadata: {
                        source: 'web_test',
                        browser: navigator.userAgent,
                        timestamp: new Date().toISOString()
                    }
                };

                const response = await fetch(`${recordingServerUrl}/api/signal/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(taskData)
                });

                const result = await response.json();

                if (result.code === 0) {
                    isRecording = true;
                    recordingStartTime = Date.now();

                    // 更新UI
                    document.getElementById('startRecordingBtn').disabled = true;
                    document.getElementById('stopRecordingBtn').disabled = false;

                    updateRecordingStatus('recording', '录制中');

                    // 启动时长计时器
                    startRecordingDurationTimer();

                    addLog(`录制开始成功，任务ID: ${currentTaskId}`, 'success');
                    addLog(`WebRTC URL: ${result.data.webrtc_url}`, 'info');

                } else {
                    updateRecordingStatus('error', '启动失败');
                    addLog(`录制启动失败: ${result.message}`, 'error');
                    currentTaskId = null;
                }

            } catch (error) {
                updateRecordingStatus('error', '连接失败');
                addLog(`录制服务连接失败: ${error.message}`, 'error');
                addLog('请确认录制服务已启动 (端口5410)', 'warning');
                currentTaskId = null;
            }
        }

        // 停止录制
        async function stopRecording() {
            if (!isRecording) {
                addLog('当前未在录制', 'warning');
                return;
            }

            try {
                if (!currentTaskId) {
                    addLog('无有效的录制任务ID', 'error');
                    return;
                }

                addLog('停止录制...', 'info');
                updateRecordingStatus('stopping', '停止中...');

                const response = await fetch(`${recordingServerUrl}/api/signal/stop`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_id: currentTaskId
                    })
                });

                const result = await response.json();

                if (result.code === 0) {
                    isRecording = false;
                    recordingStartTime = null;

                    // 停止计时器
                    stopRecordingDurationTimer();

                    // 更新UI
                    document.getElementById('startRecordingBtn').disabled = false;
                    document.getElementById('stopRecordingBtn').disabled = true;

                    updateRecordingStatus('stopped', '已停止');

                    addLog(`录制停止成功，任务ID: ${currentTaskId}`, 'success');
                    addLog(`录制时长: ${result.data.duration}`, 'info');
                    addLog(`文件大小: ${result.data.file_size}`, 'info');

                    // 清理任务ID
                    currentTaskId = null;

                } else {
                    addLog(`录制停止失败: ${result.message}`, 'error');
                }

            } catch (error) {
                addLog(`录制服务连接失败: ${error.message}`, 'error');
            }
        }

        // ==================== 视频录制功能 ====================

        // 开始视频录制
        async function startVideoRecording() {
            if (!isStreaming) {
                addLog('请先开始推流', 'warning');
                return;
            }

            if (isVideoRecording) {
                addLog('视频录制已在进行中', 'warning');
                return;
            }

            try {
                const streamKey = document.getElementById('streamKey').value;
                if (!streamKey) {
                    addLog('请输入推流密钥', 'error');
                    return;
                }

                addLog('开始视频录制...', 'info');

                // 生成任务ID
                const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
                currentVideoTaskId = `VIDEO_${timestamp}`;

                const requestData = {
                    task_id: currentVideoTaskId,
                    stream_key: streamKey,
                    userid: 'web_user',
                    username: '网页用户',
                    chief_complaint: 'WebRTC视频录制测试',
                    present_illness: '通过网页进行WebRTC推流视频录制功能测试',
                    past_medical_history: '无',
                    allergic_history: '无',
                    metadata: {
                        timestamp: new Date().toISOString(),
                        browser: navigator.userAgent,
                        recording_type: 'video'
                    }
                };

                const response = await fetch(`${recordingServerUrl}/api/recording/video/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (result.code === 0) {
                    isVideoRecording = true;
                    videoRecordingStartTime = Date.now();

                    // 更新UI状态
                    updateVideoRecordingStatus('recording', '录制中');
                    document.getElementById('startVideoRecordingBtn').disabled = true;
                    document.getElementById('stopVideoRecordingBtn').disabled = false;

                    // 启动时长计时器
                    startVideoRecordingDurationTimer();

                    addLog(`视频录制开始成功，任务ID: ${currentVideoTaskId}`, 'success');
                    if (result.data && result.data.session_id) {
                        addLog(`会话ID: ${result.data.session_id}`, 'info');
                    }
                } else {
                    addLog(`视频录制开始失败: ${result.message}`, 'error');
                    updateVideoRecordingStatus('error', '启动失败');
                }

            } catch (error) {
                addLog(`视频录制开始异常: ${error.message}`, 'error');

                // 重置状态
                isVideoRecording = false;
                videoRecordingStartTime = null;
                currentVideoTaskId = null;
            }
        }

        // 停止视频录制
        async function stopVideoRecording() {
            if (!isVideoRecording) {
                addLog('当前未在进行视频录制', 'warning');
                return;
            }

            try {
                if (!currentVideoTaskId) {
                    addLog('无有效的视频录制任务ID', 'error');
                    return;
                }

                addLog('停止视频录制...', 'info');

                const response = await fetch(`${recordingServerUrl}/api/recording/video/stop`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_id: currentVideoTaskId
                    })
                });

                const result = await response.json();

                if (result.code === 0) {
                    // 更新UI状态
                    updateVideoRecordingStatus('stopped', '已停止');
                    document.getElementById('startVideoRecordingBtn').disabled = false;
                    document.getElementById('stopVideoRecordingBtn').disabled = true;

                    // 停止时长计时器
                    stopVideoRecordingDurationTimer();

                    addLog(`视频录制停止成功`, 'success');

                    if (result.data) {
                        if (result.data.duration) {
                            addLog(`录制时长: ${result.data.duration}`, 'info');
                        }
                        if (result.data.output_file) {
                            addLog(`录制文件: ${result.data.output_file}`, 'info');
                            document.getElementById('videoRecordingFile').textContent = result.data.output_file;
                        }
                        if (result.data.file_size) {
                            addLog(`文件大小: ${(result.data.file_size / 1024 / 1024).toFixed(2)} MB`, 'info');
                        }
                    }
                } else {
                    addLog(`视频录制停止失败: ${result.message}`, 'error');
                }

            } catch (error) {
                addLog(`视频录制停止异常: ${error.message}`, 'error');
            } finally {
                // 重置状态
                isVideoRecording = false;
                videoRecordingStartTime = null;
                currentVideoTaskId = null;
            }
        }

        // ==================== 音频录制功能 ====================

        // 开始音频录制
        async function startAudioRecording() {
            if (!isStreaming) {
                addLog('请先开始推流', 'warning');
                return;
            }

            if (isAudioRecording) {
                addLog('音频录制已在进行中', 'warning');
                return;
            }

            try {
                const streamKey = document.getElementById('streamKey').value;
                if (!streamKey) {
                    addLog('请输入推流密钥', 'error');
                    return;
                }

                addLog('开始音频录制...', 'info');

                // 生成任务ID
                const timestamp = new Date().toISOString().replace(/[-:T]/g, '').slice(0, 14);
                currentAudioTaskId = `AUDIO_${timestamp}`;

                const requestData = {
                    task_id: currentAudioTaskId,
                    stream_key: streamKey,
                    userid: 'web_user',
                    username: '网页用户',
                    chief_complaint: 'WebRTC音频录制测试',
                    present_illness: '通过网页进行WebRTC推流音频录制功能测试',
                    past_medical_history: '无',
                    allergic_history: '无',
                    metadata: {
                        timestamp: new Date().toISOString(),
                        browser: navigator.userAgent,
                        recording_type: 'audio'
                    }
                };

                const response = await fetch(`${recordingServerUrl}/api/recording/audio/start`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();

                if (result.code === 0) {
                    isAudioRecording = true;
                    audioRecordingStartTime = Date.now();

                    // 更新UI状态
                    updateAudioRecordingStatus('recording', '录制中');
                    document.getElementById('startAudioRecordingBtn').disabled = true;
                    document.getElementById('stopAudioRecordingBtn').disabled = false;

                    // 启动时长计时器
                    startAudioRecordingDurationTimer();

                    addLog(`音频录制开始成功，任务ID: ${currentAudioTaskId}`, 'success');
                    if (result.data && result.data.session_id) {
                        addLog(`会话ID: ${result.data.session_id}`, 'info');
                    }
                } else {
                    addLog(`音频录制开始失败: ${result.message}`, 'error');
                    updateAudioRecordingStatus('error', '启动失败');
                }

            } catch (error) {
                addLog(`音频录制开始异常: ${error.message}`, 'error');

                // 重置状态
                isAudioRecording = false;
                audioRecordingStartTime = null;
                currentAudioTaskId = null;
            }
        }

        // 停止音频录制
        async function stopAudioRecording() {
            if (!isAudioRecording) {
                addLog('当前未在进行音频录制', 'warning');
                return;
            }

            try {
                if (!currentAudioTaskId) {
                    addLog('无有效的音频录制任务ID', 'error');
                    return;
                }

                addLog('停止音频录制...', 'info');

                const response = await fetch(`${recordingServerUrl}/api/recording/audio/stop`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        task_id: currentAudioTaskId
                    })
                });

                const result = await response.json();

                if (result.code === 0) {
                    // 更新UI状态
                    updateAudioRecordingStatus('stopped', '已停止');
                    document.getElementById('startAudioRecordingBtn').disabled = false;
                    document.getElementById('stopAudioRecordingBtn').disabled = true;

                    // 停止时长计时器
                    stopAudioRecordingDurationTimer();

                    addLog(`音频录制停止成功`, 'success');

                    if (result.data) {
                        if (result.data.duration) {
                            addLog(`录制时长: ${result.data.duration}`, 'info');
                        }
                        if (result.data.output_file) {
                            addLog(`录制文件: ${result.data.output_file}`, 'info');
                            document.getElementById('audioRecordingFile').textContent = result.data.output_file;
                        }
                        if (result.data.file_size) {
                            addLog(`文件大小: ${(result.data.file_size / 1024 / 1024).toFixed(2)} MB`, 'info');
                        }
                    }
                } else {
                    addLog(`音频录制停止失败: ${result.message}`, 'error');
                }

            } catch (error) {
                addLog(`音频录制停止异常: ${error.message}`, 'error');
            } finally {
                // 重置状态
                isAudioRecording = false;
                audioRecordingStartTime = null;
                currentAudioTaskId = null;
            }
        }

        // 更新录制状态显示
        function updateRecordingStatus(status, text) {
            const statusElement = document.getElementById('recordingStatus');
            statusElement.textContent = text || status;

            // 移除所有状态类
            statusElement.className = 'status-indicator';

            // 添加对应的状态类
            switch (status) {
                case 'stopped':
                    statusElement.classList.add('status-stopped');
                    break;
                case 'starting':
                    statusElement.classList.add('status-starting');
                    break;
                case 'recording':
                    statusElement.classList.add('status-recording');
                    break;
                case 'stopping':
                    statusElement.classList.add('status-stopping');
                    break;
                case 'error':
                    statusElement.classList.add('status-error');
                    break;
            }
        }

        // 更新视频录制状态显示
        function updateVideoRecordingStatus(status, text) {
            const statusElement = document.getElementById('videoRecordingStatus');
            statusElement.textContent = text || status;

            // 移除所有状态类
            statusElement.className = 'status-indicator';

            // 添加对应的状态类
            switch (status) {
                case 'stopped':
                    statusElement.classList.add('status-stopped');
                    break;
                case 'starting':
                    statusElement.classList.add('status-starting');
                    break;
                case 'recording':
                    statusElement.classList.add('status-recording');
                    break;
                case 'stopping':
                    statusElement.classList.add('status-stopping');
                    break;
                case 'error':
                    statusElement.classList.add('status-error');
                    break;
            }
        }

        // 更新音频录制状态显示
        function updateAudioRecordingStatus(status, text) {
            const statusElement = document.getElementById('audioRecordingStatus');
            statusElement.textContent = text || status;

            // 移除所有状态类
            statusElement.className = 'status-indicator';

            // 添加对应的状态类
            switch (status) {
                case 'stopped':
                    statusElement.classList.add('status-stopped');
                    break;
                case 'starting':
                    statusElement.classList.add('status-starting');
                    break;
                case 'recording':
                    statusElement.classList.add('status-recording');
                    break;
                case 'stopping':
                    statusElement.classList.add('status-stopping');
                    break;
                case 'error':
                    statusElement.classList.add('status-error');
                    break;
            }
        }

        // 启动录制时长计时器
        function startRecordingDurationTimer() {
            recordingDurationTimer = setInterval(() => {
                if (recordingStartTime) {
                    const duration = Date.now() - recordingStartTime;
                    const formatted = formatDuration(duration);
                    document.getElementById('recordingDuration').textContent = formatted;
                }
            }, 1000);
        }

        // 停止录制时长计时器
        function stopRecordingDurationTimer() {
            if (recordingDurationTimer) {
                clearInterval(recordingDurationTimer);
                recordingDurationTimer = null;
            }
            document.getElementById('recordingDuration').textContent = '00:00:00';
        }

        // 启动视频录制时长计时器
        function startVideoRecordingDurationTimer() {
            videoRecordingDurationTimer = setInterval(() => {
                if (videoRecordingStartTime) {
                    const duration = Date.now() - videoRecordingStartTime;
                    const formatted = formatDuration(duration);
                    document.getElementById('videoRecordingDuration').textContent = formatted;
                }
            }, 1000);
        }

        // 停止视频录制时长计时器
        function stopVideoRecordingDurationTimer() {
            if (videoRecordingDurationTimer) {
                clearInterval(videoRecordingDurationTimer);
                videoRecordingDurationTimer = null;
            }
            document.getElementById('videoRecordingDuration').textContent = '00:00:00';
        }

        // 启动音频录制时长计时器
        function startAudioRecordingDurationTimer() {
            audioRecordingDurationTimer = setInterval(() => {
                if (audioRecordingStartTime) {
                    const duration = Date.now() - audioRecordingStartTime;
                    const formatted = formatDuration(duration);
                    document.getElementById('audioRecordingDuration').textContent = formatted;
                }
            }, 1000);
        }

        // 停止音频录制时长计时器
        function stopAudioRecordingDurationTimer() {
            if (audioRecordingDurationTimer) {
                clearInterval(audioRecordingDurationTimer);
                audioRecordingDurationTimer = null;
            }
            document.getElementById('audioRecordingDuration').textContent = '00:00:00';
        }



        // 页面加载完成后初始化录制控制
        function initializeRecordingControls() {
            // 初始化录制状态
            updateVideoRecordingStatus('stopped', '未录制');
            updateAudioRecordingStatus('stopped', '未录制');
            enableRecordingControls(false);

            // 测试录制服务连接
            fetch(`${recordingServerUrl}/api/health`)
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        addLog('录制服务连接正常', 'success');
                    }
                })
                .catch(error => {
                    addLog('录制服务连接失败，请确认服务已启动', 'warning');
                });
        }
    </script>
</body>
</html>
