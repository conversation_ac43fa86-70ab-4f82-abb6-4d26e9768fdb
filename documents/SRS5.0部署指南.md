# SRS 5.0 部署指南

## 1. 环境准备

### 系统要求
- Ubuntu 18.04+ / CentOS 7+ / Debian 9+
- GCC 4.8+ 或 Clang 3.5+
- 2GB+ 内存，2核+ CPU

### 安装依赖

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install -y build-essential git wget curl
sudo apt install -y gcc g++ make autoconf automake libtool
sudo apt install -y libssl-dev pkg-config zlib1g-dev
```

**CentOS/RHEL:**
```bash
sudo yum update -y
sudo yum groupinstall -y "Development Tools"
sudo yum install -y git wget curl
sudo yum install -y gcc gcc-c++ make autoconf automake libtool
sudo yum install -y openssl-devel pkgconfig zlib-devel
```

## 2. 获取源码

```bash
# 创建工作目录
sudo mkdir -p /opt/srs
cd /opt/srs

# 克隆SRS仓库
sudo git clone https://github.com/ossrs/srs.git
cd srs

# 切换到SRS 5.0稳定版本
sudo git checkout v5.0-r3

# 验证版本
git describe --tags
```

## 3. 编译配置

```bash
cd trunk

# 配置编译选项
sudo ./configure \
    --with-ssl \
    --with-webrtc \
    --with-rtc \
    --with-hls \
    --with-dvr \
    --with-http-callback \
    --with-http-server \
    --with-http-api

# 开始编译
sudo make -j$(nproc)
```

## 4. 创建配置文件

创建SRS配置文件 `/opt/srs/srs/trunk/conf/srs.conf`：

```nginx
# SRS 5.0 配置文件
listen              1935;
max_connections     1000;
srs_log_tank        file;
srs_log_file        ./objs/srs.log;
srs_log_level       info;

# HTTP服务器
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
}

# HTTP API
http_api {
    enabled         on;
    listen          1985;
    crossdomain     on;
}

# WebRTC服务器
rtc_server {
    enabled         on;
    listen          8000;
    # 替换为你的服务器IP
    candidate       YOUR_SERVER_IP;
}

# 默认虚拟主机
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled     on;
        rtmp        on;
        nack        on;
        pli         on;
        twcc        on;
    }
    
    # RTMP配置
    rtmp {
        enabled     on;
    }
    
    # HLS配置
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
    
    # DVR录制配置
    dvr {
        enabled         on;
        dvr_path        ./objs/nginx/html/[app]/[stream]/[2006]/[01]/[02]/[stream]-[15].[04].[05].flv;
        dvr_plan        session;
        dvr_duration    30;
        dvr_wait_keyframe   on;
    }
}
```

## 5. 修改配置文件

**重要：修改服务器IP地址**

```bash
# 替换配置文件中的IP地址（替换为你的实际服务器IP）
sudo sed -i 's/YOUR_SERVER_IP/**************/g' /opt/srs/srs/trunk/conf/srs.conf
```

## 6. 启动SRS服务

### 方法1：直接启动（测试用）

```bash
cd /opt/srs/srs/trunk

# 启动SRS
sudo ./objs/srs -c conf/srs.conf

# 后台启动
sudo ./objs/srs -c conf/srs.conf -d
```

### 方法2：systemd服务（推荐）

创建systemd服务文件：

```bash
sudo tee /etc/systemd/system/srs.service << 'EOF'
[Unit]
Description=SRS Media Server
After=network.target

[Service]
Type=forking
WorkingDirectory=/opt/srs/srs/trunk
ExecStart=/opt/srs/srs/trunk/objs/srs -c /opt/srs/srs/trunk/conf/srs.conf -d
ExecReload=/bin/kill -HUP $MAINPID
ExecStop=/bin/kill -TERM $MAINPID
PIDFile=/opt/srs/srs/trunk/objs/srs.pid
Restart=always
RestartSec=3

[Install]
WantedBy=multi-user.target
EOF

# 启用并启动服务
sudo systemctl daemon-reload
sudo systemctl enable srs
sudo systemctl start srs

# 查看服务状态
sudo systemctl status srs
```

## 7. 验证部署

### 检查进程
```bash
ps aux | grep srs
```

### 检查端口
```bash
ss -tlnp | grep -E "(1935|8080|1985|8000)"
```

### 测试API
```bash
# 测试HTTP API
curl http://localhost:1985/api/v1/summaries

# 测试WebRTC API
curl -X POST http://localhost:1985/rtc/v1/publish/ \
  -H "Content-Type: application/json" \
  -d '{
    "api": "http://localhost:1985/rtc/v1/publish/",
    "streamurl": "webrtc://localhost:8000/live/test",
    "sdp": "test"
  }'
```

## 8. 防火墙配置

### Ubuntu/Debian (UFW)
```bash
sudo ufw allow 1935/tcp    # RTMP
sudo ufw allow 8080/tcp    # HTTP
sudo ufw allow 1985/tcp    # API
sudo ufw allow 8000/udp    # WebRTC
```

### CentOS/RHEL (firewalld)
```bash
sudo firewall-cmd --permanent --add-port=1935/tcp
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --permanent --add-port=1985/tcp
sudo firewall-cmd --permanent --add-port=8000/udp
sudo firewall-cmd --reload
```

## 9. 服务管理

```bash
# 启动服务
sudo systemctl start srs

# 停止服务
sudo systemctl stop srs

# 重启服务
sudo systemctl restart srs

# 查看状态
sudo systemctl status srs

# 查看日志
sudo journalctl -u srs -f

# 查看SRS日志
tail -f /opt/srs/srs/trunk/objs/srs.log
```

## 10. 测试推流

使用FFmpeg测试RTMP推流：

```bash
# 安装FFmpeg
sudo apt install ffmpeg  # Ubuntu/Debian
sudo yum install ffmpeg  # CentOS/RHEL

# 测试推流
ffmpeg -f lavfi -i testsrc=duration=10:size=320x240:rate=30 \
  -f lavfi -i sine=frequency=1000:duration=10 \
  -c:v libx264 -c:a aac -f flv rtmp://localhost:1935/live/test
```

## 11. 常见问题

### 服务启动失败
```bash
# 检查配置文件语法
./objs/srs -t -c conf/srs.conf

# 检查端口占用
ss -tlnp | grep -E "(1935|8000|1985|8080)"
```

### WebRTC连接失败
- 确保candidate配置为正确的服务器IP
- 检查UDP端口8000是否开放
- 验证防火墙配置

### 权限问题
```bash
# 修改文件权限
sudo chown -R $USER:$USER /opt/srs/srs/trunk/objs/
sudo chmod +x /opt/srs/srs/trunk/objs/srs
```

## 完成

SRS 5.0部署完成！现在可以：
- RTMP推流：`rtmp://YOUR_SERVER_IP:1935/live/STREAM_KEY`
- WebRTC推流：`webrtc://YOUR_SERVER_IP:8000/live/STREAM_KEY`
- HTTP-FLV播放：`http://YOUR_SERVER_IP:8080/live/STREAM_KEY.flv`
- HLS播放：`http://YOUR_SERVER_IP:8080/live/STREAM_KEY.m3u8`
