# 专门用于接收前端WebRTC推流

# 基础服务配置
listen              5401;           # RTMP监听端口
max_connections     1000;           # 最大连接数
daemon              off;            # 前台运行，便于调试
srs_log_tank        file;           # 日志输出到文件
srs_log_file        /data/MedicalRecord/logs/srs/srs.log;
srs_log_level       info;           # 日志级别

# HTTP服务器配置
http_server {
    enabled         on;
    listen          5402;           # HTTP服务端口
    dir             ./objs/nginx/html;
    crossdomain     on;
}

# HTTP API配置
http_api {
    enabled         on;
    listen          5403;           # HTTP API端口
    crossdomain     on;
    raw_api {
        enabled     on;
        allow_reload on;
        allow_query  on;
        allow_update on;
    }
}

# WebRTC服务器配置
rtc_server {
    enabled         on;             # 启用WebRTC服务器
    listen          5400;           # WebRTC UDP监听端口
    candidate       *;              # 自动检测服务器IP
}

# 默认虚拟主机配置
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled         on;         # 启用WebRTC
        rtmp_to_rtc     off;        # 禁用RTMP到WebRTC转换
        rtc_to_rtmp     off;        # 禁用WebRTC到RTMP转换
        nack            on;         # 启用NACK（丢包重传）
        twcc            on;         # 启用TWCC（拥塞控制）
        stun_timeout    30;         # STUN超时时间
        dtls_role       passive;    # DTLS角色：被动模式
    }
    
    # HTTP-FLV配置 - 供录制服务拉取流
    http_remux {
        enabled         on;         # 启用HTTP-FLV
        mount           [vhost]/[app]/[stream].flv;
    }
    
    # 禁用DVR录制
    dvr {
        enabled         off;        # 禁用SRS内置录制功能
    }
    
    # 推流回调配置
    http_hooks {
        enabled         on;         # 启用HTTP回调
        on_publish      http://127.0.0.1:5000/api/hooks/on_publish;
        on_unpublish    http://127.0.0.1:5000/api/hooks/on_unpublish;
    }
}

# 配置说明：
# 1. 端口配置：WebRTC(5400), RTMP(5401), HTTP(5402), API(5403)
# 2. 禁用SRS内置DVR录制，录制由独立FFmpeg服务完成
# 3. 启用HTTP-FLV，供录制服务拉取流使用
# 4. 配置推流回调，用于监控推流状态
